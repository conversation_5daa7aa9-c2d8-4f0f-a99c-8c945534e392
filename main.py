import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Import các module đã tạo
from core.adb_controller import ADBController
from core.image_recognition import ImageRecognition
from ui.main_window import MainWindow

class AutoToolApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setStyle('Fusion')  # Modern style
        
        # Khởi tạo các components
        self.adb_controller = ADBController()
        self.image_recognition = ImageRecognition()
        
        # Load các template mặc định (nếu có)
        self.load_default_templates()
        
        # Tạo main window
        self.main_window = MainWindow(self.adb_controller, self.image_recognition)
        
    def load_default_templates(self):
        """Tự động load tất cả templates từ thư mục resources/images"""
        import os
        import glob

        template_dir = "resources/images"
        if not os.path.exists(template_dir):
            print(f"Thư mục template không tồn tại: {template_dir}")
            return

        # Tìm tất cả file ảnh trong thư mục
        image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp']
        template_files = []

        for extension in image_extensions:
            template_files.extend(glob.glob(os.path.join(template_dir, extension)))

        if not template_files:
            print(f"Không tìm thấy template nào trong {template_dir}")
            return

        # Load từng template
        loaded_count = 0
        for file_path in template_files:
            try:
                # Lấy tên file làm tên template (bỏ extension)
                template_name = os.path.splitext(os.path.basename(file_path))[0]
                self.image_recognition.load_template(template_name, file_path)
                print(f"✓ Đã load template: {template_name}")
                loaded_count += 1
            except Exception as e:
                print(f"✗ Không thể load template từ {file_path}: {e}")

        print(f"Đã load {loaded_count}/{len(template_files)} templates từ {template_dir}")
    
    def run(self):
        """Chạy ứng dụng"""
        self.main_window.show()
        return self.app.exec_()

def main():
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = AutoToolApp()
    sys.exit(app.run())

if __name__ == '__main__':
    main()