import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Import các module đã tạo
from core.adb_controller import ADBController
from core.image_recognition import ImageRecognition
from ui.main_window import MainWindow

class AutoToolApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setStyle('Fusion')  # Modern style
        
        # Khởi tạo các components
        self.adb_controller = ADBController()
        self.image_recognition = ImageRecognition()
        
        # Load các template mặc định (nếu có)
        self.load_default_templates()
        
        # Tạo main window
        self.main_window = MainWindow(self.adb_controller, self.image_recognition)
        
    def load_default_templates(self):
        """Load các template thường dùng"""
        default_templates = {
            'boss_icon': 'resources/images/boss_icon.png',
            'quest_icon': 'resources/images/quest_icon.png',
            'item_icon': 'resources/images/item_icon.png',
            'skill_icon': 'resources/images/skill_icon.png',
            'npc_icon': 'resources/images/npc_icon.png',
            'map_icon': 'resources/images/map_icon.png',
            'dai_ly_phu': 'resources/images/dai_ly_phu.png',
            'xa_phu': 'resources/images/xa_phu.png',
            'bat_auto': 'resources/images/bat_auto.png',
        }

        for name, path in default_templates.items():
            try:
                self.image_recognition.load_template(name, path)
                print(f"Đã load template: {name}")
            except Exception as e:
                print(f"Không thể load template {name} từ {path}: {e}")
    
    def run(self):
        """Chạy ứng dụng"""
        self.main_window.show()
        return self.app.exec_()

def main():
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = AutoToolApp()
    sys.exit(app.run())

if __name__ == '__main__':
    main()