# Hướng dẫn cài đặt Auto Tool

## 🔧 Cài đặt Python

### Cách 1: Từ Microsoft Store (Khuyến nghị)
1. Mở Microsoft Store
2. T<PERSON><PERSON> kiếm "Python 3.11" hoặc "Python 3.12"
3. Click "Get" để cài đặt
4. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ong, mở Command Prompt và gõ `python --version` để kiểm tra

### Cách 2: Từ python.org
1. Truy cập https://www.python.org/downloads/
2. Tải Python 3.11 hoặc 3.12 (64-bit)
3. Chạy file cài đặt
4. **QUAN TRỌNG**: Tick vào "Add Python to PATH"
5. Click "Install Now"

## 📦 Cài đặt thư viện

Mở Command Prompt (cmd) và chạy:

```bash
# Cập nhật pip
python -m pip install --upgrade pip

# Cài đặt thư viện cần thiết
pip install PyQt5==5.15.9
pip install opencv-python==********
pip install numpy==1.24.3
pip install adbutils==1.2.15
pip install Pillow==10.0.1
```

Hoặc cài tất cả cùng lúc:
```bash
pip install -r requirements.txt
```

## 🚀 Chạy ứng dụng

### Demo UI (không cần LDPlayer)
```bash
cd auto_tool
python demo_ui.py
```

### Ứng dụng đầy đủ (cần LDPlayer)
```bash
cd auto_tool
python main.py
```

## 🎮 Chuẩn bị LDPlayer

1. **Tải và cài LDPlayer**: https://www.ldplayer.net/
2. **Bật ADB Debugging**:
   - Mở LDPlayer
   - Vào Settings → Other settings
   - Bật "ADB debugging"
   - Restart LDPlayer
3. **Kiểm tra kết nối**:
   ```bash
   adb devices
   ```
   Sẽ hiển thị danh sách LDPlayer đang chạy

## 🔍 Troubleshooting

### Lỗi "Python was not found"
- Cài đặt Python từ Microsoft Store hoặc python.org
- Đảm bảo Python đã được thêm vào PATH

### Lỗi "No module named 'PyQt5'"
```bash
pip install PyQt5
```

### Lỗi "No module named 'cv2'"
```bash
pip install opencv-python
```

### Không tìm thấy LDPlayer
- Đảm bảo LDPlayer đang chạy
- Bật ADB debugging trong LDPlayer
- Restart LDPlayer và thử lại

### ADB không kết nối được
```bash
# Restart ADB service
adb kill-server
adb start-server

# Kiểm tra devices
adb devices
```

## 📁 Cấu trúc thư mục

```
auto_tool/
├── core/                    # Logic chính
├── ui/                      # Giao diện
├── resources/
│   └── images/             # Templates (đặt ảnh template ở đây)
├── main.py                 # Chạy ứng dụng chính
├── demo_ui.py             # Demo giao diện
├── requirements.txt       # Danh sách thư viện
└── README.md             # Hướng dẫn sử dụng
```

## 🖼️ Thêm Templates

1. Chụp ảnh các icon/button trong game
2. Lưu vào thư mục `resources/images/`
3. Đặt tên file theo chức năng (ví dụ: `boss_icon.png`, `quest_button.png`)
4. Ứng dụng sẽ tự động load tất cả templates khi khởi động

## ✅ Kiểm tra cài đặt

Chạy lệnh sau để kiểm tra:
```bash
python -c "import PyQt5, cv2, numpy, adbutils; print('Tất cả thư viện đã được cài đặt!')"
```

Nếu không có lỗi, bạn đã sẵn sàng sử dụng Auto Tool!
