import subprocess
import time
from adbutils import adb
import cv2
import numpy as np
from io import BytesIO
from typing import List, Dict, Optional, Tuple

class ADBController:
    def __init__(self, device_serial=None):
        """Khởi tạo kết nối ADB với LDPlayer"""
        self.device = None
        self.device_serial = device_serial
        self.devices_cache = {}  # Cache cho multiple devices
        self.connect()
    
    def connect(self):
        """Kết nối với LDPlayer"""
        try:
            # LDPlayer thường chạy ở port 5555-5585
            if not self.device_serial:
                # Tìm LDPlayer đang chạy
                devices = adb.device_list()
                for d in devices:
                    if '5555' in d.serial or 'emulator' in d.serial:
                        self.device = d
                        self.device_serial = d.serial
                        break
            else:
                self.device = adb.device(self.device_serial)
            
            if self.device:
                print(f"Đã kết nối với thiết bị: {self.device_serial}")
                return True
            else:
                print("Không tìm thấy LDPlayer")
                return False
        except Exception as e:
            print(f"Lỗi kết nối: {e}")
            return False
    
    def tap(self, x, y):
        """Click vào tọa độ x, y"""
        if self.device:
            self.device.shell(f"input tap {x} {y}")
            time.sleep(0.1)
    
    def swipe(self, x1, y1, x2, y2, duration=300):
        """Vuốt từ (x1,y1) đến (x2,y2)"""
        if self.device:
            self.device.shell(f"input swipe {x1} {y1} {x2} {y2} {duration}")
            time.sleep(0.1)
    
    def long_press(self, x, y, duration=1000):
        """Giữ click tại vị trí"""
        if self.device:
            self.swipe(x, y, x, y, duration)
    
    def screenshot(self):
        """Chụp màn hình và trả về ảnh numpy array"""
        if self.device:
            # Lấy ảnh PIL từ adbutils
            pil_image = self.device.screenshot()

            # Chuyển PIL.Image sang bytes (dạng PNG)
            buf = BytesIO()
            pil_image.save(buf, format="PNG")
            png_data = buf.getvalue()

            # Chuyển bytes thành numpy array
            img_array = np.frombuffer(png_data, np.uint8)

            # Giải mã thành ảnh OpenCV
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

            return img
        return None
    
    def input_text(self, text):
        """Nhập text"""
        if self.device:
            # Escape special characters
            text = text.replace(' ', '%s')
            self.device.shell(f"input text '{text}'")
            time.sleep(0.1)
    
    def key_event(self, keycode):
        """Gửi phím đặc biệt
        KEYCODE_BACK = 4
        KEYCODE_HOME = 3
        KEYCODE_MENU = 82
        """
        if self.device:
            self.device.shell(f"input keyevent {keycode}")
            time.sleep(0.1)
    
    def get_screen_size(self):
        """Lấy kích thước màn hình"""
        if self.device:
            output = self.device.shell("wm size")
            # Parse output: "Physical size: 1280x720"
            if "Physical size:" in output:
                size_str = output.split("Physical size:")[1].strip()
                width, height = map(int, size_str.split('x'))
                return width, height
        return None, None

    @staticmethod
    def get_all_devices() -> List[Dict[str, str]]:
        """Lấy danh sách tất cả devices đang kết nối"""
        try:
            devices = adb.device_list()
            device_list = []
            for device in devices:
                device_info = {
                    'serial': device.serial,
                    'status': 'connected' if device.get_state() == 'device' else 'offline'
                }
                device_list.append(device_info)
            return device_list
        except Exception as e:
            print(f"Lỗi khi lấy danh sách devices: {e}")
            return []

    @staticmethod
    def find_ldplayer_devices() -> List[str]:
        """Tìm tất cả LDPlayer devices"""
        devices = ADBController.get_all_devices()
        ldplayer_devices = []

        for device in devices:
            serial = device['serial']
            # LDPlayer thường có serial dạng emulator-xxxx hoặc 127.0.0.1:xxxx
            if ('emulator-' in serial or '127.0.0.1:' in serial) and device['status'] == 'connected':
                ldplayer_devices.append(serial)

        return ldplayer_devices

    def connect_to_device(self, device_serial: str) -> bool:
        """Kết nối đến device cụ thể"""
        try:
            device = adb.device(device_serial)
            if device.get_state() == 'device':
                self.devices_cache[device_serial] = device
                print(f"Đã kết nối với device: {device_serial}")
                return True
            else:
                print(f"Device {device_serial} không sẵn sàng")
                return False
        except Exception as e:
            print(f"Lỗi kết nối device {device_serial}: {e}")
            return False

    def get_device(self, device_serial: str = None):
        """Lấy device object"""
        if device_serial is None:
            device_serial = self.device_serial

        if device_serial in self.devices_cache:
            return self.devices_cache[device_serial]
        elif device_serial == self.device_serial:
            return self.device
        else:
            # Thử kết nối mới
            if self.connect_to_device(device_serial):
                return self.devices_cache[device_serial]
        return None

    def tap_on_device(self, x: int, y: int, device_serial: str = None):
        """Click vào tọa độ x, y trên device cụ thể"""
        device = self.get_device(device_serial)
        if device:
            device.shell(f"input tap {x} {y}")
            time.sleep(0.1)

    def screenshot_from_device(self, device_serial: str = None) -> Optional[np.ndarray]:
        """Chụp màn hình từ device cụ thể"""
        device = self.get_device(device_serial)
        if device:
            try:
                # Lấy ảnh PIL từ adbutils
                pil_image = device.screenshot()

                # Chuyển PIL.Image sang bytes (dạng PNG)
                buf = BytesIO()
                pil_image.save(buf, format="PNG")
                png_data = buf.getvalue()

                # Chuyển bytes thành numpy array
                img_array = np.frombuffer(png_data, np.uint8)

                # Giải mã thành ảnh OpenCV
                img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

                return img
            except Exception as e:
                print(f"Lỗi chụp màn hình device {device_serial}: {e}")
        return None

    def input_text_on_device(self, text: str, device_serial: str = None):
        """Nhập text trên device cụ thể"""
        device = self.get_device(device_serial)
        if device:
            # Escape special characters
            text = text.replace(' ', '%s')
            device.shell(f"input text '{text}'")
            time.sleep(0.1)