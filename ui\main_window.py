from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QTextEdit, QGroupBox,
                            QCheckBox, QSpinBox, QComboBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog,
                            QTabWidget, QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QImage
import cv2
import numpy as np
from typing import List
from core.auto_train import AutoTrain
from core.ldplayer_manager import LDPlayerManager
from core.multi_player_auto import MultiPlayerAutoThread
from ui.screen_viewer_dialog import ScreenViewerDialog

class AutoThread(QThread):
    log_signal = pyqtSignal(str)
    screenshot_signal = pyqtSignal(np.ndarray)
    
    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.running = False
        self.tasks = []
    
    def add_task(self, task):
        self.tasks.append(task)
    
    def clear_tasks(self):
        self.tasks = []
    
    def run(self):
        self.running = True
        while self.running:
            for task in self.tasks:
                if not self.running:
                    break
                
                # Chụp màn hình
                screenshot = self.adb.screenshot()
                if screenshot is not None:
                    self.screenshot_signal.emit(screenshot)
                    
                    # Thực hiện task
                    if task['type'] == 'click_image':
                        pos = self.image_rec.find_template(screenshot, task['template'])
                        if pos:
                            self.adb.tap(pos[0], pos[1])
                            self.log_signal.emit(f"Đã click vào {task['template']} tại {pos}")
                        else:
                            self.log_signal.emit(f"Không tìm thấy {task['template']}")
                    
                    elif task['type'] == 'wait_and_click':
                        # Đợi cho đến khi tìm thấy
                        max_wait = task.get('timeout', 10)
                        waited = 0
                        while waited < max_wait and self.running:
                            screenshot = self.adb.screenshot()
                            pos = self.image_rec.find_template(screenshot, task['template'])
                            if pos:
                                self.adb.tap(pos[0], pos[1])
                                self.log_signal.emit(f"Đã click vào {task['template']}")
                                break
                            self.msleep(1000)
                            waited += 1
                
                # Delay giữa các action
                self.msleep(task.get('delay', 1000))
    
    def stop(self):
        self.running = False

class AutoTrainThread(QThread):
    log_signal = pyqtSignal(str)
    stats_signal = pyqtSignal(dict)
    
    def __init__(self, auto_train):
        super().__init__()
        self.auto_train = auto_train
        self.train_spot = None
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.emit_stats)
        
    def set_train_spot(self, spot_key):
        self.train_spot = spot_key
    
    def run(self):
        self.stats_timer.start(5000)  # Update stats every 5s
        self.auto_train.start_train(self.train_spot)
    
    def emit_stats(self):
        stats = self.auto_train.get_train_stats()
        self.stats_signal.emit(stats)
    
    def stop(self):
        self.stats_timer.stop()
        self.auto_train.stop()

class MainWindow(QMainWindow):
    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.auto_thread = AutoThread(adb_controller, image_recognition)
        self.auto_train = AutoTrain(adb_controller, image_recognition)
        self.auto_train_thread = AutoTrainThread(self.auto_train)

        # Multi-player support
        self.ldplayer_manager = LDPlayerManager()
        self.multi_player_auto = MultiPlayerAutoThread(self.ldplayer_manager, adb_controller, image_recognition)
        self.screen_viewer_dialog = None
        self.current_selected_player = None

        self.init_ui()
        self.setup_connections()

        # Bắt đầu monitor LDPlayer
        self.ldplayer_manager.start_monitoring()

        # Bắt đầu multi-player auto thread
        self.multi_player_auto.start()

        # Làm mới danh sách players ban đầu
        self.refresh_ldplayers()

        # Timer để cập nhật bảng LDPlayer định kỳ
        self.ldplayer_update_timer = QTimer()
        self.ldplayer_update_timer.timeout.connect(self.update_ldplayer_table)
        self.ldplayer_update_timer.start(10000)  # Cập nhật mỗi 10 giây

        # Hiển thị danh sách templates
        self.refresh_templates_list()
    
    def init_ui(self):
        self.setWindowTitle("Auto Tool - Võ Lâm Truyền Kỳ")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls with Tabs
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Quick controls
        quick_controls_group = QGroupBox("Điều khiển nhanh")
        quick_controls_layout = QVBoxLayout()

        # Multi-player controls
        multi_layout = QHBoxLayout()
        self.show_screens_btn = QPushButton("Xem danh sách màn hình")
        self.show_screens_btn.clicked.connect(self.show_screen_viewer)
        multi_layout.addWidget(self.show_screens_btn)

        self.refresh_players_btn = QPushButton("Làm mới LDPlayer")
        self.refresh_players_btn.clicked.connect(self.refresh_ldplayers)
        multi_layout.addWidget(self.refresh_players_btn)
        quick_controls_layout.addLayout(multi_layout)

        # Player selection
        quick_controls_layout.addWidget(QLabel("LDPlayer hiện tại:"))
        self.player_combo = QComboBox()
        self.player_combo.currentTextChanged.connect(self.on_player_selected)
        quick_controls_layout.addWidget(self.player_combo)

        self.status_label = QLabel("Chưa chọn LDPlayer")
        quick_controls_layout.addWidget(self.status_label)

        quick_controls_group.setLayout(quick_controls_layout)
        left_layout.addWidget(quick_controls_group)
        
        # Tab widget for different auto features
        self.tab_widget = QTabWidget()
        
        # Tab 1: Auto Features
        auto_tab = QWidget()
        auto_tab_layout = QVBoxLayout()
        
        # Auto features group
        auto_group = QGroupBox("Chức năng Auto")
        auto_layout = QVBoxLayout()
        
        # Auto hunt
        self.auto_hunt_cb = QCheckBox("Auto săn boss")
        auto_layout.addWidget(self.auto_hunt_cb)
        
        # Auto quest
        self.auto_quest_cb = QCheckBox("Auto nhiệm vụ")
        auto_layout.addWidget(self.auto_quest_cb)
        
        # Auto pick items
        self.auto_pick_cb = QCheckBox("Auto nhặt đồ")
        auto_layout.addWidget(self.auto_pick_cb)
        
        # Auto use skills
        self.auto_skill_cb = QCheckBox("Auto sử dụng skill")
        auto_layout.addWidget(self.auto_skill_cb)
        
        auto_group.setLayout(auto_layout)
        auto_tab_layout.addWidget(auto_group)
        
        # Control buttons
        control_group = QGroupBox("Điều khiển")
        control_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("Bắt đầu Auto")
        self.start_btn.clicked.connect(self.start_auto)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("Dừng Auto")
        self.stop_btn.clicked.connect(self.stop_auto)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_group.setLayout(control_layout)
        auto_tab_layout.addWidget(control_group)
        auto_tab_layout.addStretch()
        auto_tab.setLayout(auto_tab_layout)
        
        # Tab 2: Auto Train
        train_tab = QWidget()
        train_tab_layout = QVBoxLayout()
        
        # Train settings
        train_settings_group = QGroupBox("Cài đặt Train")
        train_settings_layout = QVBoxLayout()
        
        # Chọn bãi train
        train_settings_layout.addWidget(QLabel("Chọn bãi train:"))
        self.train_spot_combo = QComboBox()
        self.train_spot_combo.addItems([
            "bai_1 - Bãi train cấp 1-20",
            "bai_2 - Bãi train cấp 20-40"
        ])
        train_settings_layout.addWidget(self.train_spot_combo)
        
        # Auto return when dead
        self.auto_return_cb = QCheckBox("Tự động quay lại bãi khi chết")
        self.auto_return_cb.setChecked(True)
        train_settings_layout.addWidget(self.auto_return_cb)
        
        # HP/MP settings
        hp_layout = QHBoxLayout()
        hp_layout.addWidget(QLabel("Dùng HP khi dưới:"))
        self.hp_threshold_spin = QSpinBox()
        self.hp_threshold_spin.setRange(10, 90)
        self.hp_threshold_spin.setValue(50)
        self.hp_threshold_spin.setSuffix("%")
        hp_layout.addWidget(self.hp_threshold_spin)
        train_settings_layout.addLayout(hp_layout)
        
        mp_layout = QHBoxLayout()
        mp_layout.addWidget(QLabel("Dùng MP khi dưới:"))
        self.mp_threshold_spin = QSpinBox()
        self.mp_threshold_spin.setRange(10, 90)
        self.mp_threshold_spin.setValue(30)
        self.mp_threshold_spin.setSuffix("%")
        mp_layout.addWidget(self.mp_threshold_spin)
        train_settings_layout.addLayout(mp_layout)
        
        train_settings_group.setLayout(train_settings_layout)
        train_tab_layout.addWidget(train_settings_group)
        
        # Train control
        train_control_group = QGroupBox("Điều khiển Train")
        train_control_layout = QVBoxLayout()

        # Single player controls
        single_layout = QHBoxLayout()
        self.start_train_btn = QPushButton("Bắt đầu Train (Player hiện tại)")
        self.start_train_btn.clicked.connect(self.start_train)
        single_layout.addWidget(self.start_train_btn)

        self.stop_train_btn = QPushButton("Dừng Train (Player hiện tại)")
        self.stop_train_btn.clicked.connect(self.stop_train)
        self.stop_train_btn.setEnabled(False)
        single_layout.addWidget(self.stop_train_btn)
        train_control_layout.addLayout(single_layout)

        # Multi-player controls
        multi_layout = QHBoxLayout()
        self.start_all_train_btn = QPushButton("Bắt đầu Train (Tất cả)")
        self.start_all_train_btn.clicked.connect(self.start_all_train)
        multi_layout.addWidget(self.start_all_train_btn)

        self.stop_all_train_btn = QPushButton("Dừng Train (Tất cả)")
        self.stop_all_train_btn.clicked.connect(self.stop_all_train)
        multi_layout.addWidget(self.stop_all_train_btn)
        train_control_layout.addLayout(multi_layout)

        train_control_group.setLayout(train_control_layout)
        train_tab_layout.addWidget(train_control_group)
        
        # Train statistics
        train_stats_group = QGroupBox("Thống kê Train")
        train_stats_layout = QVBoxLayout()
        
        self.train_duration_label = QLabel("Thời gian: 00:00:00")
        train_stats_layout.addWidget(self.train_duration_label)
        
        self.train_kills_label = QLabel("Số quái đã giết: 0")
        train_stats_layout.addWidget(self.train_kills_label)
        
        self.train_deaths_label = QLabel("Số lần chết: 0")
        train_stats_layout.addWidget(self.train_deaths_label)
        
        self.train_kph_label = QLabel("Quái/giờ: 0")
        train_stats_layout.addWidget(self.train_kph_label)
        
        train_stats_group.setLayout(train_stats_layout)
        train_tab_layout.addWidget(train_stats_group)
        
        train_tab_layout.addStretch()
        train_tab.setLayout(train_tab_layout)
        

        
        # Tab 4: LDPlayer Manager
        ldplayer_tab = QWidget()
        ldplayer_tab_layout = QVBoxLayout()

        # LDPlayer list controls
        ldplayer_controls_group = QGroupBox("Điều khiển LDPlayer")
        ldplayer_controls_layout = QHBoxLayout()

        self.refresh_list_btn = QPushButton("Làm mới danh sách")
        self.refresh_list_btn.clicked.connect(self.refresh_ldplayer_list)
        ldplayer_controls_layout.addWidget(self.refresh_list_btn)

        self.select_all_btn = QPushButton("Chọn tất cả")
        self.select_all_btn.clicked.connect(self.select_all_players)
        ldplayer_controls_layout.addWidget(self.select_all_btn)

        self.deselect_all_btn = QPushButton("Bỏ chọn tất cả")
        self.deselect_all_btn.clicked.connect(self.deselect_all_players)
        ldplayer_controls_layout.addWidget(self.deselect_all_btn)

        ldplayer_controls_layout.addStretch()
        ldplayer_controls_group.setLayout(ldplayer_controls_layout)
        ldplayer_tab_layout.addWidget(ldplayer_controls_group)

        # LDPlayer list table
        ldplayer_list_group = QGroupBox("Danh sách LDPlayer")
        ldplayer_list_layout = QVBoxLayout()

        self.ldplayer_table = QTableWidget(0, 6)
        self.ldplayer_table.setHorizontalHeaderLabels([
            "Chọn", "Tên", "Index", "Trạng thái", "Auto", "Thao tác"
        ])
        self.ldplayer_table.horizontalHeader().setStretchLastSection(True)
        self.ldplayer_table.setSelectionBehavior(QTableWidget.SelectRows)
        ldplayer_list_layout.addWidget(self.ldplayer_table)

        ldplayer_list_group.setLayout(ldplayer_list_layout)
        ldplayer_tab_layout.addWidget(ldplayer_list_group)

        # Selected players actions
        selected_actions_group = QGroupBox("Thao tác với LDPlayer đã chọn")
        selected_actions_layout = QVBoxLayout()

        # Train settings for selected players
        selected_train_layout = QHBoxLayout()
        selected_train_layout.addWidget(QLabel("Bãi train cho players đã chọn:"))
        self.selected_train_combo = QComboBox()
        self.selected_train_combo.addItems([
            "bai_1 - Bãi train cấp 1-20",
            "bai_2 - Bãi train cấp 20-40"
        ])
        selected_train_layout.addWidget(self.selected_train_combo)
        selected_actions_layout.addLayout(selected_train_layout)

        # Action buttons for selected players
        selected_buttons_layout = QHBoxLayout()

        self.start_selected_btn = QPushButton("Bắt đầu Train (Đã chọn)")
        self.start_selected_btn.clicked.connect(self.start_selected_train)
        selected_buttons_layout.addWidget(self.start_selected_btn)

        self.stop_selected_btn = QPushButton("Dừng Train (Đã chọn)")
        self.stop_selected_btn.clicked.connect(self.stop_selected_train)
        selected_buttons_layout.addWidget(self.stop_selected_btn)

        self.launch_selected_btn = QPushButton("Khởi động (Đã chọn)")
        self.launch_selected_btn.clicked.connect(self.launch_selected_players)
        selected_buttons_layout.addWidget(self.launch_selected_btn)

        self.close_selected_btn = QPushButton("Đóng (Đã chọn)")
        self.close_selected_btn.clicked.connect(self.close_selected_players)
        selected_buttons_layout.addWidget(self.close_selected_btn)

        selected_actions_layout.addLayout(selected_buttons_layout)
        selected_actions_group.setLayout(selected_actions_layout)
        ldplayer_tab_layout.addWidget(selected_actions_group)

        ldplayer_tab_layout.addStretch()
        ldplayer_tab.setLayout(ldplayer_tab_layout)

        # Tab 4: Templates Info
        template_info_tab = QWidget()
        template_info_layout = QVBoxLayout()

        # Template info
        template_info_group = QGroupBox("Templates đã load")
        template_info_group_layout = QVBoxLayout()

        self.refresh_templates_btn = QPushButton("Làm mới danh sách Templates")
        self.refresh_templates_btn.clicked.connect(self.refresh_templates_list)
        template_info_group_layout.addWidget(self.refresh_templates_btn)

        self.templates_list = QTableWidget(0, 2)
        self.templates_list.setHorizontalHeaderLabels(["Tên Template", "Trạng thái"])
        self.templates_list.horizontalHeader().setStretchLastSection(True)
        self.templates_list.setEditTriggers(QTableWidget.NoEditTriggers)
        template_info_group_layout.addWidget(self.templates_list)

        template_info_group.setLayout(template_info_group_layout)
        template_info_layout.addWidget(template_info_group)

        template_info_layout.addStretch()
        template_info_tab.setLayout(template_info_layout)

        # Add tabs to tab widget (LDPlayer tab first)
        self.tab_widget.addTab(ldplayer_tab, "Quản lý LDPlayer")
        self.tab_widget.addTab(train_tab, "Auto Train")
        self.tab_widget.addTab(auto_tab, "Auto Cơ bản")
        self.tab_widget.addTab(template_info_tab, "Templates")
        
        left_layout.addWidget(self.tab_widget)
        
        # Screenshot button
        self.screenshot_btn = QPushButton("Chụp màn hình")
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        left_layout.addWidget(self.screenshot_btn)
        
        main_layout.addWidget(left_panel, 1)
        
        # Right panel - Display and logs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setMinimumSize(480, 270)
        self.screenshot_label.setStyleSheet("border: 1px solid black")
        self.screenshot_label.setScaledContents(True)
        right_layout.addWidget(QLabel("Màn hình:"))
        right_layout.addWidget(self.screenshot_label)
        
        # Log display
        right_layout.addWidget(QLabel("Nhật ký:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        right_layout.addWidget(self.log_text)
        
        main_layout.addWidget(right_panel, 2)
        
        # Timer for periodic screenshot
        self.screenshot_timer = QTimer()
        self.screenshot_timer.timeout.connect(self.update_screenshot)
    
    def setup_connections(self):
        self.auto_thread.log_signal.connect(self.add_log)
        self.auto_thread.screenshot_signal.connect(self.display_screenshot)
        self.auto_train_thread.log_signal.connect(self.add_log)
        self.auto_train_thread.stats_signal.connect(self.update_train_stats)

        # Multi-player auto connections
        self.multi_player_auto.log_signal.connect(self.add_log)
        self.multi_player_auto.player_log_signal.connect(self.add_player_log)
        self.multi_player_auto.stats_signal.connect(self.update_player_stats)
        self.multi_player_auto.player_status_changed.connect(self.on_player_status_changed)
    

    
    def start_auto(self):
        # Clear old tasks
        self.auto_thread.clear_tasks()
        
        # Add tasks based on selected features
        if self.auto_hunt_cb.isChecked():
            self.auto_thread.add_task({
                'type': 'click_image',
                'template': 'boss_icon',
                'delay': 2000
            })
        
        if self.auto_quest_cb.isChecked():
            self.auto_thread.add_task({
                'type': 'wait_and_click',
                'template': 'quest_icon',
                'timeout': 10
            })
        
        # Start thread
        self.auto_thread.start()
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.add_log("Đã bắt đầu auto")
    
    def stop_auto(self):
        self.auto_thread.stop()
        self.auto_thread.wait()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("Đã dừng auto")
    
    def take_screenshot(self):
        screenshot = self.adb.screenshot()
        if screenshot is not None:
            self.display_screenshot(screenshot)
            # Save screenshot
            cv2.imwrite("screenshot.png", screenshot)
            self.add_log("Đã chụp màn hình")
    
    def update_screenshot(self):
        if not self.auto_thread.isRunning():
            screenshot = self.adb.screenshot()
            if screenshot is not None:
                self.display_screenshot(screenshot)
    
    def display_screenshot(self, screenshot):
        # Convert numpy array to QPixmap
        height, width, channel = screenshot.shape
        bytes_per_line = 3 * width
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        
        q_image = QImage(rgb_image.data, width, height, 
                        bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to fit label
        scaled_pixmap = pixmap.scaled(self.screenshot_label.size(), 
                                     Qt.KeepAspectRatio, 
                                     Qt.SmoothTransformation)
        self.screenshot_label.setPixmap(scaled_pixmap)
    

    
    def add_log(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def start_train(self):
        """Bắt đầu auto train"""
        spot_text = self.train_spot_combo.currentText()
        spot_key = spot_text.split(" - ")[0]
        
        self.auto_train_thread.set_train_spot(spot_key)
        self.auto_train_thread.start()
        
        self.start_train_btn.setEnabled(False)
        self.stop_train_btn.setEnabled(True)
        self.add_log(f"Bắt đầu train tại: {spot_text}")
    
    def stop_train(self):
        """Dừng auto train"""
        self.auto_train_thread.stop()
        self.auto_train_thread.wait()
        
        self.start_train_btn.setEnabled(True)
        self.stop_train_btn.setEnabled(False)
        self.add_log("Đã dừng train")
    
    def update_train_stats(self, stats):
        """Cập nhật thống kê train"""
        if 'duration' in stats:
            self.train_duration_label.setText(f"Thời gian: {stats['duration']}")
        if 'kill_count' in stats:
            self.train_kills_label.setText(f"Số quái đã giết: {stats['kill_count']}")
        if 'death_count' in stats:
            self.train_deaths_label.setText(f"Số lần chết: {stats['death_count']}")
        if 'kills_per_hour' in stats:
            self.train_kph_label.setText(f"Quái/giờ: {stats['kills_per_hour']}")
    
    def show_screen_viewer(self):
        """Hiển thị dialog xem danh sách màn hình"""
        if not self.screen_viewer_dialog:
            self.screen_viewer_dialog = ScreenViewerDialog(self.ldplayer_manager, self)
            self.screen_viewer_dialog.player_selected.connect(self.on_player_selected_from_viewer)

        self.screen_viewer_dialog.refresh_players()
        self.screen_viewer_dialog.show()
        self.screen_viewer_dialog.raise_()
        self.screen_viewer_dialog.activateWindow()

    def refresh_ldplayers(self):
        """Làm mới danh sách LDPlayer"""
        self.ldplayer_manager.refresh_players()
        self.update_player_combo()
        self.update_ldplayer_table()  # Cập nhật bảng LDPlayer

        player_count = len(self.ldplayer_manager.get_player_list())
        if player_count == 0:
            self.add_log("⚠️ Không tìm thấy LDPlayer nào đang chạy")
            self.add_log("💡 Hướng dẫn: 1) Mở LDPlayer 2) Bật ADB debugging 3) Click 'Làm mới'")
        else:
            self.add_log(f"✅ Tìm thấy {player_count} LDPlayer có thể sử dụng")

    def update_player_combo(self):
        """Cập nhật combo box chọn player"""
        current_text = self.player_combo.currentText()
        self.player_combo.clear()

        players = self.ldplayer_manager.get_player_list()

        if len(players) == 0:
            self.player_combo.addItem("Không có LDPlayer nào đang chạy", None)
            self.status_label.setText("Không có LDPlayer")
            self.status_label.setStyleSheet("color: red")
            return

        for player in players:
            # Chỉ hiển thị những player connected
            if player['status'] == 'connected':
                display_text = f"{player['name']} (Sẵn sàng)"
                self.player_combo.addItem(display_text, player['serial'])

        # Nếu không có player nào connected
        if self.player_combo.count() == 0:
            self.player_combo.addItem("Không có LDPlayer nào sẵn sàng", None)
            self.status_label.setText("Không có LDPlayer sẵn sàng")
            self.status_label.setStyleSheet("color: orange")
            return

        # Khôi phục lựa chọn cũ nếu có
        if current_text:
            index = self.player_combo.findText(current_text)
            if index >= 0:
                self.player_combo.setCurrentIndex(index)

    def on_player_selected(self, text):
        """Xử lý khi chọn player từ combo box"""
        if not text:
            self.status_label.setText("Chưa chọn LDPlayer")
            self.status_label.setStyleSheet("color: gray")
            return

        index = self.player_combo.currentIndex()
        if index >= 0:
            serial = self.player_combo.itemData(index)
            self.current_selected_player = serial

            # Cập nhật ADB controller để sử dụng player này
            self.adb.device_serial = serial
            if self.adb.connect_to_device(serial):
                self.status_label.setText(f"Đã chọn: {text}")
                self.status_label.setStyleSheet("color: green")
                self.add_log(f"Đã chọn player: {text}")
                # Bắt đầu cập nhật screenshot
                self.screenshot_timer.start(1000)
            else:
                self.status_label.setText(f"Không thể kết nối: {text}")
                self.status_label.setStyleSheet("color: red")

    def on_player_selected_from_viewer(self, serial):
        """Xử lý khi chọn player từ screen viewer"""
        # Tìm và chọn player trong combo box
        for i in range(self.player_combo.count()):
            if self.player_combo.itemData(i) == serial:
                self.player_combo.setCurrentIndex(i)
                break

        self.add_log(f"Đã chọn player từ viewer: {serial}")

    def refresh_templates_list(self):
        """Làm mới danh sách templates"""
        # Lấy danh sách templates từ image recognition
        templates = getattr(self.image_rec, 'templates', {})

        self.templates_list.setRowCount(len(templates))

        for row, (name, template_data) in enumerate(templates.items()):
            # Tên template
            name_item = QTableWidgetItem(name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.templates_list.setItem(row, 0, name_item)

            # Trạng thái
            status = "✓ Đã load" if template_data is not None else "✗ Lỗi"
            status_item = QTableWidgetItem(status)
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)

            if template_data is not None:
                status_item.setBackground(Qt.green)
            else:
                status_item.setBackground(Qt.red)

            self.templates_list.setItem(row, 1, status_item)

        # Resize columns
        self.templates_list.resizeColumnsToContents()

        self.add_log(f"Đã load {len(templates)} templates")

    def refresh_ldplayer_list(self):
        """Làm mới danh sách LDPlayer trong table"""
        self.ldplayer_manager.refresh_players()
        self.update_ldplayer_table()
        self.add_log("Đã làm mới danh sách LDPlayer")

    def update_ldplayer_table(self):
        """Cập nhật bảng danh sách LDPlayer"""
        players = self.ldplayer_manager.get_player_list()
        self.ldplayer_table.setRowCount(len(players))

        if len(players) == 0:
            # Hiển thị thông báo hướng dẫn khi không có LDPlayer
            self.ldplayer_table.setRowCount(3)

            # Thông báo chính
            no_player_item = QTableWidgetItem("❌ Không tìm thấy LDPlayer nào đang chạy và có thể kết nối")
            no_player_item.setFlags(no_player_item.flags() & ~Qt.ItemIsEditable)
            no_player_item.setBackground(Qt.yellow)
            self.ldplayer_table.setItem(0, 0, no_player_item)
            self.ldplayer_table.setSpan(0, 0, 1, 6)

            # Hướng dẫn 1
            guide1_item = QTableWidgetItem("💡 Bước 1: Mở LDPlayer và đảm bảo nó đang chạy")
            guide1_item.setFlags(guide1_item.flags() & ~Qt.ItemIsEditable)
            guide1_item.setBackground(Qt.lightGray)
            self.ldplayer_table.setItem(1, 0, guide1_item)
            self.ldplayer_table.setSpan(1, 0, 1, 6)

            # Hướng dẫn 2
            guide2_item = QTableWidgetItem("💡 Bước 2: Bật ADB debugging trong LDPlayer Settings → Other → ADB debugging")
            guide2_item.setFlags(guide2_item.flags() & ~Qt.ItemIsEditable)
            guide2_item.setBackground(Qt.lightGray)
            self.ldplayer_table.setItem(2, 0, guide2_item)
            self.ldplayer_table.setSpan(2, 0, 1, 6)

            return

        for row, player in enumerate(players):
            # Checkbox chọn player
            checkbox = QCheckBox()
            checkbox.setProperty("serial", player['serial'])
            self.ldplayer_table.setCellWidget(row, 0, checkbox)

            # Tên player
            name_item = QTableWidgetItem(player['name'])
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.ldplayer_table.setItem(row, 1, name_item)

            # Index
            index_item = QTableWidgetItem(str(player['index']))
            index_item.setFlags(index_item.flags() & ~Qt.ItemIsEditable)
            self.ldplayer_table.setItem(row, 2, index_item)

            # Trạng thái
            status_item = QTableWidgetItem(player['status'])
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            # Màu sắc theo trạng thái
            if player['status'] == 'connected':
                status_item.setBackground(Qt.green)
            elif player['status'] == 'running':
                status_item.setBackground(Qt.yellow)
            elif player['status'] == 'stopped':
                status_item.setBackground(Qt.red)
            self.ldplayer_table.setItem(row, 3, status_item)

            # Trạng thái Auto
            auto_status = "Đang chạy" if player.get('auto_running', False) else "Dừng"
            auto_item = QTableWidgetItem(auto_status)
            auto_item.setFlags(auto_item.flags() & ~Qt.ItemIsEditable)
            if player.get('auto_running', False):
                auto_item.setBackground(Qt.green)
            self.ldplayer_table.setItem(row, 4, auto_item)

            # Nút thao tác
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)

            # Nút kết nối
            connect_btn = QPushButton("Kết nối")
            connect_btn.setMaximumWidth(60)
            connect_btn.clicked.connect(lambda checked, s=player['serial']: self.connect_single_player(s))
            action_layout.addWidget(connect_btn)

            # Nút xem màn hình
            view_btn = QPushButton("Xem")
            view_btn.setMaximumWidth(50)
            view_btn.clicked.connect(lambda checked, s=player['serial']: self.view_single_player(s))
            action_layout.addWidget(view_btn)

            self.ldplayer_table.setCellWidget(row, 5, action_widget)

        # Resize columns
        self.ldplayer_table.resizeColumnsToContents()

    def select_all_players(self):
        """Chọn tất cả players"""
        for row in range(self.ldplayer_table.rowCount()):
            checkbox = self.ldplayer_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.add_log("Đã chọn tất cả LDPlayer")

    def deselect_all_players(self):
        """Bỏ chọn tất cả players"""
        for row in range(self.ldplayer_table.rowCount()):
            checkbox = self.ldplayer_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.add_log("Đã bỏ chọn tất cả LDPlayer")

    def get_selected_players(self) -> List[str]:
        """Lấy danh sách serial của players đã chọn"""
        selected = []
        for row in range(self.ldplayer_table.rowCount()):
            checkbox = self.ldplayer_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                serial = checkbox.property("serial")
                if serial:
                    selected.append(serial)
        return selected

    def start_selected_train(self):
        """Bắt đầu train cho players đã chọn"""
        selected_players = self.get_selected_players()
        if not selected_players:
            self.add_log("Chưa chọn LDPlayer nào")
            return

        spot_text = self.selected_train_combo.currentText()
        spot_key = spot_text.split(" - ")[0]

        success_count = 0
        for serial in selected_players:
            if self.multi_player_auto.start_auto_for_player(serial, spot_key):
                success_count += 1

        self.add_log(f"Đã bắt đầu train cho {success_count}/{len(selected_players)} players đã chọn")
        self.update_ldplayer_table()  # Cập nhật trạng thái

    def stop_selected_train(self):
        """Dừng train cho players đã chọn"""
        selected_players = self.get_selected_players()
        if not selected_players:
            self.add_log("Chưa chọn LDPlayer nào")
            return

        for serial in selected_players:
            self.multi_player_auto.stop_auto_for_player(serial)

        self.add_log(f"Đã dừng train cho {len(selected_players)} players đã chọn")
        self.update_ldplayer_table()  # Cập nhật trạng thái

    def launch_selected_players(self):
        """Khởi động players đã chọn"""
        selected_players = self.get_selected_players()
        if not selected_players:
            self.add_log("Chưa chọn LDPlayer nào")
            return

        success_count = 0
        for serial in selected_players:
            if self.ldplayer_manager.start_ldplayer(serial):
                success_count += 1

        self.add_log(f"Đã khởi động {success_count}/{len(selected_players)} players đã chọn")
        # Đợi một chút rồi cập nhật lại
        QTimer.singleShot(3000, self.update_ldplayer_table)

    def close_selected_players(self):
        """Đóng players đã chọn"""
        selected_players = self.get_selected_players()
        if not selected_players:
            self.add_log("Chưa chọn LDPlayer nào")
            return

        # Dừng auto trước khi đóng
        for serial in selected_players:
            self.multi_player_auto.stop_auto_for_player(serial)

        success_count = 0
        for serial in selected_players:
            if self.ldplayer_manager.stop_ldplayer(serial):
                success_count += 1

        self.add_log(f"Đã đóng {success_count}/{len(selected_players)} players đã chọn")
        # Đợi một chút rồi cập nhật lại
        QTimer.singleShot(2000, self.update_ldplayer_table)

    def connect_single_player(self, serial: str):
        """Kết nối ADB với một player"""
        if self.adb.connect_to_device(serial):
            self.add_log(f"Đã kết nối ADB với {serial}")
            self.update_ldplayer_table()
        else:
            self.add_log(f"Không thể kết nối ADB với {serial}")

    def view_single_player(self, serial: str):
        """Xem màn hình của một player"""
        # Chọn player này trong combo box chính
        for i in range(self.player_combo.count()):
            if self.player_combo.itemData(i) == serial:
                self.player_combo.setCurrentIndex(i)
                break

        # Chụp và hiển thị screenshot
        screenshot = self.adb.screenshot_from_device(serial)
        if screenshot is not None:
            self.display_screenshot(screenshot)
            self.add_log(f"Đã chụp màn hình {serial}")
        else:
            self.add_log(f"Không thể chụp màn hình {serial}")

    def start_all_train(self):
        """Bắt đầu train cho tất cả LDPlayer"""
        spot_text = self.train_spot_combo.currentText()
        spot_key = spot_text.split(" - ")[0]

        self.multi_player_auto.start_auto_for_all(spot_key)
        self.add_log(f"Đã yêu cầu bắt đầu train tất cả players tại: {spot_text}")

    def stop_all_train(self):
        """Dừng train cho tất cả LDPlayer"""
        self.multi_player_auto.stop_auto_for_all()
        self.add_log("Đã yêu cầu dừng train tất cả players")

    def add_player_log(self, serial: str, message: str):
        """Thêm log cho player cụ thể"""
        if serial in self.ldplayer_manager.players:
            player_name = self.ldplayer_manager.players[serial].name
            self.add_log(f"[{player_name}] {message}")
        else:
            self.add_log(f"[{serial}] {message}")

    def update_player_stats(self, serial: str, stats: dict):
        """Cập nhật thống kê cho player cụ thể"""
        # Nếu đây là player hiện tại được chọn, cập nhật UI
        if serial == self.current_selected_player:
            self.update_train_stats(stats)

        # Log thống kê
        if stats and 'kill_count' in stats:
            player_name = self.ldplayer_manager.players.get(serial, {}).name if serial in self.ldplayer_manager.players else serial
            self.add_log(f"[{player_name}] Đã giết: {stats['kill_count']}, Thời gian: {stats.get('duration', 'N/A')}")

    def on_player_status_changed(self, serial: str, status: str):
        """Xử lý khi trạng thái player thay đổi"""
        if serial in self.ldplayer_manager.players:
            player_name = self.ldplayer_manager.players[serial].name
            self.add_log(f"[{player_name}] Trạng thái: {status}")

    def closeEvent(self, event):
        self.stop_auto()
        self.stop_train()
        self.screenshot_timer.stop()
        self.ldplayer_update_timer.stop()

        # Dừng multi-player auto
        self.multi_player_auto.stop()
        self.multi_player_auto.wait(5000)

        # Dừng monitoring
        self.ldplayer_manager.stop_monitoring()

        if self.screen_viewer_dialog:
            self.screen_viewer_dialog.close()

        event.accept()