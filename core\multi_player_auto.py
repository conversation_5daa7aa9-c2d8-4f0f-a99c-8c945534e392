import threading
import time
from typing import Dict, List, Optional
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal
from core.auto_train import AutoTrain
from core.adb_controller import ADBController
from core.image_recognition import ImageRecognition
from core.ldplayer_manager import LDPlayerManager

class PlayerAutoWorker(QThread):
    """Worker thread cho một LDPlayer cụ thể"""
    log_signal = pyqtSignal(str, str)  # serial, message
    stats_signal = pyqtSignal(str, dict)  # serial, stats
    status_changed = pyqtSignal(str, str)  # serial, status
    
    def __init__(self, serial: str, adb_controller: ADBController, image_recognition: ImageRecognition):
        super().__init__()
        self.serial = serial
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.auto_train = None
        self.running = False
        self.train_spot = None
        self.stats_timer = None
        
    def set_train_spot(self, spot_key: str):
        """<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> bãi train"""
        self.train_spot = spot_key
        
    def run(self):
        """Chạy auto train cho player này"""
        try:
            self.running = True
            self.status_changed.emit(self.serial, "starting")
            
            # Tạo auto train instance riêng cho player này
            self.auto_train = AutoTrain(self.adb, self.image_rec)
            
            # Override các phương thức để sử dụng device cụ thể
            self._setup_device_specific_methods()
            
            self.log_signal.emit(self.serial, f"Bắt đầu auto train tại {self.train_spot}")
            self.status_changed.emit(self.serial, "running")
            
            # Bắt đầu train
            self.auto_train.start_train(self.train_spot)
            
        except Exception as e:
            self.log_signal.emit(self.serial, f"Lỗi: {e}")
            self.status_changed.emit(self.serial, "error")
        finally:
            self.running = False
            self.status_changed.emit(self.serial, "stopped")
    
    def _setup_device_specific_methods(self):
        """Thiết lập các phương thức để sử dụng device cụ thể"""
        original_screenshot = self.auto_train.adb.screenshot
        original_tap = self.auto_train.adb.tap
        original_input_text = self.auto_train.adb.input_text
        
        # Override screenshot
        def device_screenshot():
            return self.adb.screenshot_from_device(self.serial)
        
        # Override tap
        def device_tap(x, y):
            self.adb.tap_on_device(x, y, self.serial)
        
        # Override input_text
        def device_input_text(text):
            self.adb.input_text_on_device(text, self.serial)
        
        self.auto_train.adb.screenshot = device_screenshot
        self.auto_train.adb.tap = device_tap
        self.auto_train.adb.input_text = device_input_text
    
    def stop(self):
        """Dừng auto train"""
        self.running = False
        if self.auto_train:
            self.auto_train.stop()
    
    def get_stats(self) -> dict:
        """Lấy thống kê"""
        if self.auto_train:
            return self.auto_train.get_train_stats()
        return {}

class MultiPlayerAutoThread(QThread):
    """Thread quản lý auto cho nhiều LDPlayer"""
    log_signal = pyqtSignal(str)  # message
    player_log_signal = pyqtSignal(str, str)  # serial, message
    stats_signal = pyqtSignal(str, dict)  # serial, stats
    player_status_changed = pyqtSignal(str, str)  # serial, status
    
    def __init__(self, ldplayer_manager: LDPlayerManager, adb_controller: ADBController, image_recognition: ImageRecognition):
        super().__init__()
        self.ldplayer_manager = ldplayer_manager
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.player_workers: Dict[str, PlayerAutoWorker] = {}
        self.running = False
        self.stats_update_timer = None
        
    def start_auto_for_player(self, serial: str, train_spot: str):
        """Bắt đầu auto cho một player"""
        if serial in self.player_workers:
            self.stop_auto_for_player(serial)
        
        # Kiểm tra player có tồn tại và kết nối không
        if serial not in self.ldplayer_manager.players:
            self.log_signal.emit(f"Player {serial} không tồn tại")
            return False
        
        player_info = self.ldplayer_manager.players[serial]
        if player_info.status != "connected":
            self.log_signal.emit(f"Player {player_info.name} chưa kết nối ADB")
            return False
        
        # Tạo worker cho player
        worker = PlayerAutoWorker(serial, self.adb, self.image_rec)
        worker.set_train_spot(train_spot)
        
        # Kết nối signals
        worker.log_signal.connect(self.player_log_signal)
        worker.stats_signal.connect(self.stats_signal)
        worker.status_changed.connect(self.player_status_changed)
        
        # Lưu và bắt đầu
        self.player_workers[serial] = worker
        worker.start()
        
        # Cập nhật trạng thái trong manager
        self.ldplayer_manager.set_auto_status(serial, True)
        
        self.log_signal.emit(f"Đã bắt đầu auto cho {player_info.name}")
        return True
    
    def stop_auto_for_player(self, serial: str):
        """Dừng auto cho một player"""
        if serial in self.player_workers:
            worker = self.player_workers[serial]
            worker.stop()
            worker.wait(5000)  # Đợi tối đa 5 giây
            
            if worker.isRunning():
                worker.terminate()
                worker.wait()
            
            del self.player_workers[serial]
            
            # Cập nhật trạng thái
            self.ldplayer_manager.set_auto_status(serial, False)
            
            if serial in self.ldplayer_manager.players:
                player_name = self.ldplayer_manager.players[serial].name
                self.log_signal.emit(f"Đã dừng auto cho {player_name}")
    
    def start_auto_for_all(self, train_spot: str):
        """Bắt đầu auto cho tất cả players đang kết nối"""
        connected_players = [
            serial for serial, player in self.ldplayer_manager.players.items()
            if player.status == "connected"
        ]
        
        if not connected_players:
            self.log_signal.emit("Không có LDPlayer nào đang kết nối")
            return
        
        success_count = 0
        for serial in connected_players:
            if self.start_auto_for_player(serial, train_spot):
                success_count += 1
        
        self.log_signal.emit(f"Đã bắt đầu auto cho {success_count}/{len(connected_players)} players")
    
    def stop_auto_for_all(self):
        """Dừng auto cho tất cả players"""
        players_to_stop = list(self.player_workers.keys())
        
        for serial in players_to_stop:
            self.stop_auto_for_player(serial)
        
        self.log_signal.emit(f"Đã dừng auto cho {len(players_to_stop)} players")
    
    def get_running_players(self) -> List[str]:
        """Lấy danh sách players đang chạy auto"""
        return [
            serial for serial, worker in self.player_workers.items()
            if worker.isRunning()
        ]
    
    def get_all_stats(self) -> Dict[str, dict]:
        """Lấy thống kê của tất cả players"""
        stats = {}
        for serial, worker in self.player_workers.items():
            stats[serial] = worker.get_stats()
        return stats
    
    def run(self):
        """Thread chính để monitor và cập nhật stats"""
        self.running = True
        
        while self.running:
            try:
                # Cập nhật stats cho tất cả players
                for serial, worker in self.player_workers.items():
                    if worker.isRunning():
                        stats = worker.get_stats()
                        if stats:
                            self.stats_signal.emit(serial, stats)
                            # Cập nhật vào manager
                            self.ldplayer_manager.update_train_stats(serial, stats)
                
                # Kiểm tra workers đã dừng
                finished_workers = [
                    serial for serial, worker in self.player_workers.items()
                    if worker.isFinished()
                ]
                
                for serial in finished_workers:
                    self.stop_auto_for_player(serial)
                
                self.msleep(5000)  # Cập nhật mỗi 5 giây
                
            except Exception as e:
                self.log_signal.emit(f"Lỗi trong MultiPlayerAutoThread: {e}")
                self.msleep(5000)
    
    def stop(self):
        """Dừng thread chính"""
        self.running = False
        self.stop_auto_for_all()
    
    def is_player_running(self, serial: str) -> bool:
        """Kiểm tra player có đang chạy auto không"""
        return serial in self.player_workers and self.player_workers[serial].isRunning()
    
    def get_player_status(self, serial: str) -> str:
        """Lấy trạng thái auto của player"""
        if serial in self.player_workers:
            worker = self.player_workers[serial]
            if worker.isRunning():
                return "running"
            elif worker.isFinished():
                return "finished"
            else:
                return "starting"
        return "stopped"
