from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QPushButton, QLabel, QScrollArea, QWidget, 
                            QGroupBox, QCheckBox, QComboBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QPixmap, QImage, QFont
import cv2
import numpy as np
from typing import Dict, Optional
from core.ldplayer_manager import LDPlayerManager
from core.adb_controller import ADBController

class ScreenUpdateThread(QThread):
    """Thread để cập nhật screenshot real-time"""
    screenshot_updated = pyqtSignal(str, np.ndarray)  # serial, screenshot
    
    def __init__(self, ldplayer_manager: LDPlayerManager):
        super().__init__()
        self.ldplayer_manager = ldplayer_manager
        self.running = False
        self.update_interval = 2  # Cập nhật mỗi 2 giây
        
    def run(self):
        self.running = True
        while self.running:
            try:
                screenshots = self.ldplayer_manager.get_all_screenshots()
                for serial, screenshot in screenshots.items():
                    self.screenshot_updated.emit(serial, screenshot)
                self.msleep(self.update_interval * 1000)
            except Exception as e:
                print(f"Lỗi trong ScreenUpdateThread: {e}")
                self.msleep(5000)
    
    def stop(self):
        self.running = False

class PlayerScreenWidget(QFrame):
    """Widget hiển thị màn hình của một LDPlayer"""
    player_selected = pyqtSignal(str)  # serial
    start_auto_requested = pyqtSignal(str)  # serial
    stop_auto_requested = pyqtSignal(str)  # serial
    
    def __init__(self, serial: str, player_info: dict):
        super().__init__()
        self.serial = serial
        self.player_info = player_info
        self.init_ui()
        
    def init_ui(self):
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(2)
        self.setFixedSize(320, 240)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Header với tên player và trạng thái
        header_layout = QHBoxLayout()
        
        self.name_label = QLabel(self.player_info['name'])
        self.name_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(self.name_label)
        
        self.status_label = QLabel(self.player_info['status'])
        self.status_label.setStyleSheet(self._get_status_style(self.player_info['status']))
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setFixedSize(300, 170)
        self.screenshot_label.setStyleSheet("border: 1px solid gray; background-color: black;")
        self.screenshot_label.setScaledContents(True)
        self.screenshot_label.setAlignment(Qt.AlignCenter)
        self.screenshot_label.setText("Đang tải...")
        layout.addWidget(self.screenshot_label)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.select_btn = QPushButton("Chọn")
        self.select_btn.clicked.connect(lambda: self.player_selected.emit(self.serial))
        button_layout.addWidget(self.select_btn)
        
        self.auto_checkbox = QCheckBox("Auto")
        self.auto_checkbox.setChecked(self.player_info.get('auto_running', False))
        self.auto_checkbox.stateChanged.connect(self._on_auto_changed)
        button_layout.addWidget(self.auto_checkbox)
        
        layout.addLayout(button_layout)
        
        # Click để chọn player
        self.mousePressEvent = lambda event: self.player_selected.emit(self.serial)
    
    def _get_status_style(self, status: str) -> str:
        """Lấy style cho status label"""
        colors = {
            'connected': 'color: green; font-weight: bold;',
            'running': 'color: orange; font-weight: bold;',
            'stopped': 'color: red; font-weight: bold;',
            'disconnected': 'color: gray; font-weight: bold;'
        }
        return colors.get(status, 'color: black;')
    
    def _on_auto_changed(self, state):
        """Xử lý khi checkbox auto thay đổi"""
        if state == Qt.Checked:
            self.start_auto_requested.emit(self.serial)
        else:
            self.stop_auto_requested.emit(self.serial)
    
    def update_screenshot(self, screenshot: np.ndarray):
        """Cập nhật screenshot"""
        try:
            # Resize screenshot để fit vào label
            height, width = screenshot.shape[:2]
            target_width, target_height = 300, 170
            
            # Tính tỷ lệ để giữ aspect ratio
            ratio = min(target_width / width, target_height / height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            
            resized = cv2.resize(screenshot, (new_width, new_height))
            
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
            
            # Convert to QImage
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # Convert to QPixmap và hiển thị
            pixmap = QPixmap.fromImage(qt_image)
            self.screenshot_label.setPixmap(pixmap)
            
        except Exception as e:
            print(f"Lỗi cập nhật screenshot cho {self.serial}: {e}")
            self.screenshot_label.setText("Lỗi hiển thị")
    
    def update_status(self, status: str):
        """Cập nhật trạng thái"""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(self._get_status_style(status))
    
    def set_selected(self, selected: bool):
        """Đánh dấu widget được chọn"""
        if selected:
            self.setStyleSheet("QFrame { border: 3px solid blue; }")
        else:
            self.setStyleSheet("QFrame { border: 2px solid gray; }")

class ScreenViewerDialog(QDialog):
    """Dialog hiển thị danh sách màn hình LDPlayer"""
    player_selected = pyqtSignal(str)  # serial của player được chọn
    
    def __init__(self, ldplayer_manager: LDPlayerManager, parent=None):
        super().__init__(parent)
        self.ldplayer_manager = ldplayer_manager
        self.player_widgets: Dict[str, PlayerScreenWidget] = {}
        self.selected_player = None
        self.screen_update_thread = None
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        self.setWindowTitle("Danh sách màn hình LDPlayer")
        self.setModal(False)  # Cho phép tương tác với cửa sổ chính
        self.resize(1200, 800)
        
        layout = QVBoxLayout(self)
        
        # Control panel
        control_panel = QGroupBox("Điều khiển")
        control_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("Làm mới")
        self.refresh_btn.clicked.connect(self.refresh_players)
        control_layout.addWidget(self.refresh_btn)
        
        self.start_all_btn = QPushButton("Bắt đầu tất cả")
        self.start_all_btn.clicked.connect(self.start_all_auto)
        control_layout.addWidget(self.start_all_btn)
        
        self.stop_all_btn = QPushButton("Dừng tất cả")
        self.stop_all_btn.clicked.connect(self.stop_all_auto)
        control_layout.addWidget(self.stop_all_btn)
        
        control_layout.addStretch()
        
        self.selected_label = QLabel("Chưa chọn player nào")
        control_layout.addWidget(self.selected_label)
        
        control_panel.setLayout(control_layout)
        layout.addWidget(control_panel)
        
        # Scroll area cho danh sách players
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.players_widget = QWidget()
        self.players_layout = QGridLayout(self.players_widget)
        self.players_layout.setSpacing(10)
        
        scroll_area.setWidget(self.players_widget)
        layout.addWidget(scroll_area)
        
        # Close button
        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
    def setup_connections(self):
        """Thiết lập kết nối signals"""
        pass
    
    def refresh_players(self):
        """Làm mới danh sách players"""
        self.ldplayer_manager.refresh_players()
        self.update_player_widgets()
        
        # Khởi động thread cập nhật screenshot nếu chưa có
        if not self.screen_update_thread or not self.screen_update_thread.isRunning():
            self.start_screenshot_updates()
    
    def update_player_widgets(self):
        """Cập nhật widgets hiển thị players"""
        # Xóa widgets cũ
        for widget in self.player_widgets.values():
            widget.setParent(None)
        self.player_widgets.clear()
        
        # Tạo widgets mới
        players = self.ldplayer_manager.get_player_list()
        cols = 3  # 3 cột
        
        for i, player in enumerate(players):
            row = i // cols
            col = i % cols
            
            widget = PlayerScreenWidget(player['serial'], player)
            widget.player_selected.connect(self.on_player_selected)
            widget.start_auto_requested.connect(self.on_start_auto)
            widget.stop_auto_requested.connect(self.on_stop_auto)
            
            self.players_layout.addWidget(widget, row, col)
            self.player_widgets[player['serial']] = widget
    
    def start_screenshot_updates(self):
        """Bắt đầu cập nhật screenshot"""
        if self.screen_update_thread:
            self.screen_update_thread.stop()
            self.screen_update_thread.wait()
        
        self.screen_update_thread = ScreenUpdateThread(self.ldplayer_manager)
        self.screen_update_thread.screenshot_updated.connect(self.on_screenshot_updated)
        self.screen_update_thread.start()
    
    def on_screenshot_updated(self, serial: str, screenshot: np.ndarray):
        """Xử lý khi screenshot được cập nhật"""
        if serial in self.player_widgets:
            self.player_widgets[serial].update_screenshot(screenshot)
    
    def on_player_selected(self, serial: str):
        """Xử lý khi player được chọn"""
        # Bỏ chọn player cũ
        if self.selected_player and self.selected_player in self.player_widgets:
            self.player_widgets[self.selected_player].set_selected(False)
        
        # Chọn player mới
        self.selected_player = serial
        if serial in self.player_widgets:
            self.player_widgets[serial].set_selected(True)
            player_info = self.ldplayer_manager.players[serial]
            self.selected_label.setText(f"Đã chọn: {player_info.name}")
        
        # Emit signal
        self.player_selected.emit(serial)
    
    def on_start_auto(self, serial: str):
        """Xử lý yêu cầu bắt đầu auto"""
        self.ldplayer_manager.set_auto_status(serial, True)
        print(f"Bắt đầu auto cho {serial}")
    
    def on_stop_auto(self, serial: str):
        """Xử lý yêu cầu dừng auto"""
        self.ldplayer_manager.set_auto_status(serial, False)
        print(f"Dừng auto cho {serial}")
    
    def start_all_auto(self):
        """Bắt đầu auto cho tất cả players"""
        for serial in self.player_widgets:
            self.on_start_auto(serial)
    
    def stop_all_auto(self):
        """Dừng auto cho tất cả players"""
        for serial in self.player_widgets:
            self.on_stop_auto(serial)
    
    def closeEvent(self, event):
        """Xử lý khi đóng dialog"""
        if self.screen_update_thread:
            self.screen_update_thread.stop()
            self.screen_update_thread.wait()
        event.accept()
