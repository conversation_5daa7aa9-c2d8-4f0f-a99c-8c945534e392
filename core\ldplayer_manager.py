import subprocess
import time
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from adbutils import adb
import cv2
import numpy as np

@dataclass
class LDPlayerInfo:
    """Thông tin về một LDPlayer instance"""
    index: int
    name: str
    serial: str
    status: str  # "running", "stopped", "connected", "disconnected"
    resolution: Tuple[int, int] = (1920, 1080)
    last_screenshot: Optional[np.ndarray] = None
    last_update: Optional[datetime] = None
    auto_running: bool = False
    train_stats: Dict = None

class LDPlayerManager:
    """Quản lý nhiều LDPlayer instances"""
    
    def __init__(self):
        self.players: Dict[str, LDPlayerInfo] = {}
        self.ldconsole_path = self._find_ldconsole_path()
        self.screenshot_lock = threading.Lock()
        self.update_thread = None
        self.running = False
        
    def _find_ldconsole_path(self) -> Optional[str]:
        """<PERSON><PERSON><PERSON> đường dẫn đến ldconsole.exe"""
        common_paths = [
            r"C:\LDPlayer\LDPlayer4.0\ldconsole.exe",
            r"C:\LDPlayer\LDPlayer9\ldconsole.exe", 
            r"D:\LDPlayer\LDPlayer4.0\ldconsole.exe",
            r"D:\LDPlayer\LDPlayer9\ldconsole.exe"
        ]
        
        for path in common_paths:
            try:
                result = subprocess.run([path, "list2"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    return path
            except:
                continue
        return None
    
    def scan_ldplayers(self) -> List[LDPlayerInfo]:
        """Quét chỉ những LDPlayer đang chạy và có thể kết nối"""
        if not self.ldconsole_path:
            print("Không tìm thấy LDConsole")
            return []

        try:
            # Lấy danh sách LDPlayer
            result = subprocess.run([self.ldconsole_path, "list2"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                print(f"Lỗi khi lấy danh sách LDPlayer: {result.stderr}")
                return []

            # Lấy danh sách ADB devices đang kết nối
            adb_devices = self.get_adb_devices()

            players = []
            lines = result.stdout.strip().split('\n')

            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 4:
                        index = int(parts[0])
                        name = parts[1]
                        status = parts[4] if len(parts) > 4 else "unknown"

                        # Chỉ lấy những LDPlayer đang chạy
                        if status.lower() not in ['running', '1']:
                            continue

                        # Tạo serial từ index (LDPlayer thường dùng port 5555 + index)
                        serial = f"emulator-{5554 + index * 2}"

                        # Kiểm tra xem có thể kết nối ADB không
                        if serial not in adb_devices:
                            # Thử kết nối ADB
                            try:
                                device = adb.device(serial)
                                if device.get_state() != 'device':
                                    print(f"LDPlayer {name} ({serial}) không thể kết nối ADB")
                                    continue
                            except:
                                print(f"LDPlayer {name} ({serial}) không thể kết nối ADB")
                                continue

                        player_info = LDPlayerInfo(
                            index=index,
                            name=name,
                            serial=serial,
                            status='connected',  # Chỉ add những player có thể kết nối
                            train_stats={}
                        )
                        players.append(player_info)
                        print(f"Tìm thấy LDPlayer: {name} ({serial})")

            return players

        except Exception as e:
            print(f"Lỗi khi quét LDPlayer: {e}")
            return []
    
    def refresh_players(self):
        """Làm mới danh sách players - chỉ những LDPlayer đang chạy và kết nối được"""
        scanned_players = self.scan_ldplayers()

        # Xóa tất cả players cũ
        old_count = len(self.players)
        self.players.clear()

        # Thêm chỉ những players đang chạy và kết nối được
        for player in scanned_players:
            self.players[player.serial] = player

        new_count = len(self.players)
        print(f"Cập nhật danh sách LDPlayer: {old_count} → {new_count} players")

        if new_count == 0:
            print("Không tìm thấy LDPlayer nào đang chạy và có thể kết nối")
        else:
            print("LDPlayer có thể sử dụng:")
            for serial, player in self.players.items():
                print(f"  - {player.name} ({serial})")
    
    def start_ldplayer(self, serial: str) -> bool:
        """Khởi động LDPlayer"""
        if serial not in self.players:
            return False
        
        player = self.players[serial]
        try:
            result = subprocess.run([self.ldconsole_path, "launch", "--index", str(player.index)],
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                player.status = "running"
                print(f"Đã khởi động {player.name}")
                return True
            else:
                print(f"Lỗi khởi động {player.name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Lỗi khi khởi động LDPlayer {player.name}: {e}")
            return False
    
    def stop_ldplayer(self, serial: str) -> bool:
        """Dừng LDPlayer"""
        if serial not in self.players:
            return False
        
        player = self.players[serial]
        try:
            result = subprocess.run([self.ldconsole_path, "quit", "--index", str(player.index)],
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                player.status = "stopped"
                print(f"Đã dừng {player.name}")
                return True
            else:
                print(f"Lỗi khi dừng {player.name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"Lỗi khi dừng LDPlayer {player.name}: {e}")
            return False
    
    def get_adb_devices(self) -> List[str]:
        """Lấy danh sách ADB devices đang kết nối"""
        try:
            devices = adb.device_list()
            return [d.serial for d in devices]
        except Exception as e:
            print(f"Lỗi khi lấy danh sách ADB devices: {e}")
            return []
    
    def update_connection_status(self):
        """Cập nhật trạng thái kết nối ADB của các players"""
        adb_devices = self.get_adb_devices()

        # Danh sách players cần xóa (không còn kết nối được)
        players_to_remove = []

        for serial, player in self.players.items():
            if serial in adb_devices:
                # Kiểm tra kết nối thực tế
                try:
                    device = adb.device(serial)
                    if device.get_state() == 'device':
                        player.status = "connected"
                    else:
                        players_to_remove.append(serial)
                except:
                    players_to_remove.append(serial)
            else:
                players_to_remove.append(serial)

        # Xóa những players không còn kết nối được
        for serial in players_to_remove:
            if serial in self.players:
                print(f"Xóa LDPlayer không còn kết nối: {self.players[serial].name}")
                del self.players[serial]
    
    def get_player_screenshot(self, serial: str) -> Optional[np.ndarray]:
        """Chụp màn hình của một LDPlayer"""
        if serial not in self.players:
            return None
        
        player = self.players[serial]
        if player.status != "connected":
            return None
        
        try:
            device = adb.device(serial)
            pil_image = device.screenshot()
            
            # Chuyển PIL sang OpenCV
            import io
            buf = io.BytesIO()
            pil_image.save(buf, format="PNG")
            img_array = np.frombuffer(buf.getvalue(), np.uint8)
            screenshot = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            
            # Cập nhật cache
            with self.screenshot_lock:
                player.last_screenshot = screenshot
                player.last_update = datetime.now()
            
            return screenshot
            
        except Exception as e:
            print(f"Lỗi chụp màn hình {player.name}: {e}")
            return None
    
    def get_all_screenshots(self) -> Dict[str, np.ndarray]:
        """Chụp màn hình tất cả LDPlayer đang kết nối"""
        screenshots = {}
        
        for serial, player in self.players.items():
            if player.status == "connected":
                screenshot = self.get_player_screenshot(serial)
                if screenshot is not None:
                    screenshots[serial] = screenshot
        
        return screenshots
    
    def start_monitoring(self):
        """Bắt đầu monitor tự động"""
        if self.running:
            return
        
        self.running = True
        self.update_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.update_thread.start()
    
    def stop_monitoring(self):
        """Dừng monitor"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=2)
    
    def _monitor_loop(self):
        """Vòng lặp monitor"""
        while self.running:
            try:
                self.refresh_players()
                self.update_connection_status()
                time.sleep(5)  # Cập nhật mỗi 5 giây
            except Exception as e:
                print(f"Lỗi trong monitor loop: {e}")
                time.sleep(5)
    
    def get_player_list(self) -> List[Dict]:
        """Lấy danh sách players dưới dạng dict"""
        return [
            {
                'serial': serial,
                'name': player.name,
                'index': player.index,
                'status': player.status,
                'auto_running': player.auto_running,
                'last_update': player.last_update
            }
            for serial, player in self.players.items()
        ]
    
    def set_auto_status(self, serial: str, running: bool):
        """Cập nhật trạng thái auto của player"""
        if serial in self.players:
            self.players[serial].auto_running = running
    
    def update_train_stats(self, serial: str, stats: Dict):
        """Cập nhật thống kê train của player"""
        if serial in self.players:
            self.players[serial].train_stats = stats
