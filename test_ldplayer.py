"""
Script kiểm tra kết nối LDPlayer
Chạy script này để kiểm tra xem có LDPlayer nào đang chạy và có thể kết nối không
"""

import subprocess
import sys
import os

def check_adb():
    """Kiểm tra ADB có sẵn không"""
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ ADB đã sẵn sàng")
            return True
        else:
            print("❌ ADB không hoạt động")
            return False
    except FileNotFoundError:
        print("❌ ADB không được tìm thấy")
        print("💡 Cài đặt ADB hoặc đảm bảo LDPlayer đã được cài đặt")
        return False
    except Exception as e:
        print(f"❌ Lỗi kiểm tra ADB: {e}")
        return False

def check_adb_devices():
    """Kiểm tra các devices ADB"""
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Bỏ header
            devices = []
            for line in lines:
                if line.strip() and '\t' in line:
                    device_id, status = line.split('\t')
                    devices.append((device_id, status))
            
            if devices:
                print(f"✅ Tìm thấy {len(devices)} ADB device(s):")
                for device_id, status in devices:
                    if 'emulator' in device_id:
                        print(f"  📱 {device_id} - {status}")
                    else:
                        print(f"  📱 {device_id} - {status}")
                return devices
            else:
                print("❌ Không tìm thấy ADB device nào")
                return []
        else:
            print(f"❌ Lỗi lấy danh sách devices: {result.stderr}")
            return []
    except Exception as e:
        print(f"❌ Lỗi kiểm tra devices: {e}")
        return []

def find_ldconsole():
    """Tìm LDConsole"""
    common_paths = [
        r"C:\LDPlayer\LDPlayer4.0\ldconsole.exe",
        r"C:\LDPlayer\LDPlayer9\ldconsole.exe", 
        r"D:\LDPlayer\LDPlayer4.0\ldconsole.exe",
        r"D:\LDPlayer\LDPlayer9\ldconsole.exe"
    ]
    
    for path in common_paths:
        if os.path.exists(path):
            print(f"✅ Tìm thấy LDConsole: {path}")
            return path
    
    print("❌ Không tìm thấy LDConsole")
    print("💡 Đảm bảo LDPlayer đã được cài đặt")
    return None

def check_ldplayer_list(ldconsole_path):
    """Kiểm tra danh sách LDPlayer"""
    if not ldconsole_path:
        return []
    
    try:
        result = subprocess.run([ldconsole_path, "list2"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            players = []
            
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 4:
                        index = parts[0]
                        name = parts[1]
                        status = parts[4] if len(parts) > 4 else "unknown"
                        players.append((index, name, status))
            
            if players:
                print(f"✅ Tìm thấy {len(players)} LDPlayer:")
                for index, name, status in players:
                    status_icon = "🟢" if status == "1" else "🔴"
                    print(f"  {status_icon} {name} (Index: {index}, Status: {status})")
                return players
            else:
                print("❌ Không tìm thấy LDPlayer nào")
                return []
        else:
            print(f"❌ Lỗi lấy danh sách LDPlayer: {result.stderr}")
            return []
    except Exception as e:
        print(f"❌ Lỗi kiểm tra LDPlayer: {e}")
        return []

def test_connection():
    """Test kết nối với LDPlayer đang chạy"""
    try:
        from adbutils import adb
        devices = adb.device_list()
        
        connected_count = 0
        for device in devices:
            if 'emulator' in device.serial:
                try:
                    state = device.get_state()
                    if state == 'device':
                        print(f"✅ Có thể kết nối với {device.serial}")
                        connected_count += 1
                    else:
                        print(f"⚠️ {device.serial} - trạng thái: {state}")
                except Exception as e:
                    print(f"❌ Không thể kết nối {device.serial}: {e}")
        
        return connected_count
    except ImportError:
        print("❌ Thiếu thư viện adbutils")
        print("💡 Chạy: pip install adbutils")
        return 0
    except Exception as e:
        print(f"❌ Lỗi test kết nối: {e}")
        return 0

def main():
    print("🔍 Kiểm tra kết nối LDPlayer...")
    print("=" * 50)
    
    # 1. Kiểm tra ADB
    print("\n1️⃣ Kiểm tra ADB:")
    if not check_adb():
        print("💡 Hướng dẫn: Cài đặt LDPlayer hoặc Android SDK")
        return
    
    # 2. Kiểm tra ADB devices
    print("\n2️⃣ Kiểm tra ADB devices:")
    adb_devices = check_adb_devices()
    
    # 3. Tìm LDConsole
    print("\n3️⃣ Kiểm tra LDConsole:")
    ldconsole_path = find_ldconsole()
    
    # 4. Kiểm tra danh sách LDPlayer
    print("\n4️⃣ Kiểm tra danh sách LDPlayer:")
    ldplayers = check_ldplayer_list(ldconsole_path)
    
    # 5. Test kết nối
    print("\n5️⃣ Test kết nối:")
    connected_count = test_connection()
    
    # Tổng kết
    print("\n" + "=" * 50)
    print("📊 Tổng kết:")
    print(f"  • ADB devices: {len(adb_devices)}")
    print(f"  • LDPlayer instances: {len(ldplayers)}")
    print(f"  • Có thể kết nối: {connected_count}")
    
    if connected_count > 0:
        print("\n🎉 Sẵn sàng sử dụng Auto Tool!")
    else:
        print("\n⚠️ Cần khắc phục:")
        print("  1. Mở LDPlayer")
        print("  2. Bật ADB debugging trong Settings")
        print("  3. Restart LDPlayer")
        print("  4. Chạy lại script này")

if __name__ == "__main__":
    main()
