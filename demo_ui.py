"""
Demo UI để test giao diện mà không cần L<PERSON> thật
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import <PERSON>App<PERSON>, QMainWindow, QVBoxLayout, QWidget, QLabel
    from PyQt5.QtCore import Qt
    
    # Mock classes để test UI
    class MockADBController:
        def __init__(self):
            self.device_serial = None
            self.devices_cache = {}
        
        def connect(self):
            return True
        
        def connect_to_device(self, serial):
            return True
        
        def screenshot(self):
            return None
        
        def screenshot_from_device(self, serial):
            return None
        
        def tap(self, x, y):
            pass
        
        def tap_on_device(self, x, y, serial):
            pass
        
        def input_text(self, text):
            pass
        
        def input_text_on_device(self, text, serial):
            pass
        
        @staticmethod
        def get_all_devices():
            return [
                {'serial': 'emulator-5554', 'status': 'connected'},
                {'serial': 'emulator-5556', 'status': 'connected'},
            ]
        
        @staticmethod
        def find_ldplayer_devices():
            return ['emulator-5554', 'emulator-5556']
    
    class MockImageRecognition:
        def __init__(self):
            self.templates = {
                'dai_ly_phu': 'Mock template data',
                'xa_phu': 'Mock template data',
                'bat_auto': 'Mock template data',
                'boss_icon': 'Mock template data',
                'quest_icon': 'Mock template data',
            }

        def load_template(self, name, path):
            self.templates[name] = f"Mock template data from {path}"
            print(f"Mock: Loading template {name} from {path}")

        def find_template(self, screenshot, template_name, threshold=0.8):
            return None
    
    # Import UI sau khi đã có mock classes
    from ui.main_window import MainWindow
    
    class DemoApp:
        def __init__(self):
            self.app = QApplication(sys.argv)
            self.app.setStyle('Fusion')
            
            # Tạo mock controllers
            self.adb_controller = MockADBController()
            self.image_recognition = MockImageRecognition()
            
            # Tạo main window
            self.main_window = MainWindow(self.adb_controller, self.image_recognition)
            
            # Override một số phương thức để demo
            self.setup_demo_data()
        
        def setup_demo_data(self):
            """Thiết lập dữ liệu demo"""
            # Mock LDPlayer data
            from core.ldplayer_manager import LDPlayerInfo
            
            demo_players = {
                'emulator-5554': LDPlayerInfo(
                    index=0,
                    name='LDPlayer',
                    serial='emulator-5554',
                    status='connected'
                ),
                'emulator-5556': LDPlayerInfo(
                    index=1,
                    name='LDPlayer (1)',
                    serial='emulator-5556',
                    status='connected'
                ),
                'emulator-5558': LDPlayerInfo(
                    index=2,
                    name='LDPlayer (2)',
                    serial='emulator-5558',
                    status='connected'
                )
            }
            
            # Cập nhật vào manager
            self.main_window.ldplayer_manager.players = demo_players
            
            # Cập nhật UI
            self.main_window.update_player_combo()
            self.main_window.update_ldplayer_table()
        
        def run(self):
            self.main_window.show()
            return self.app.exec_()
    
    def main():
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = DemoApp()
        sys.exit(app.run())
    
    if __name__ == '__main__':
        main()

except ImportError as e:
    print(f"Lỗi import: {e}")
    print("Vui lòng cài đặt các thư viện cần thiết:")
    print("pip install PyQt5 opencv-python numpy adbutils Pillow")
    
    # Tạo một cửa sổ đơn giản để hiển thị lỗi
    try:
        from tkinter import Tk, Label, messagebox
        root = Tk()
        root.withdraw()
        messagebox.showerror("Lỗi", f"Thiếu thư viện: {e}\n\nVui lòng chạy:\npip install PyQt5 opencv-python numpy adbutils Pillow")
    except:
        print("Không thể hiển thị dialog lỗi")
