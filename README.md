# Auto Tool - Võ Lâm Truyền Kỳ

Công cụ tự động hóa cho game Võ Lâm Truyền Kỳ với hỗ trợ nhiều LDPlayer.

## 🚀 Tính năng chính

### ✅ Quản lý nhiều LDPlayer
- Hi<PERSON><PERSON> thị danh sách tất cả LDPlayer
- Checkbox để chọn LDPlayer muốn chạy auto
- Nút "Chọn tất cả" / "Bỏ chọn tất cả"
- Trạng thái real-time với màu sắc:
  - 🟢 **Connected**: Đã kết nối ADB
  - 🟡 **Running**: Đang chạy nhưng chưa kết nối ADB  
  - 🔴 **Stopped**: Đã dừng

### ✅ Auto Train
- Chạy auto train trên nhiều LDPlayer cùng lúc
- Chọn bãi train riêng cho từng nhóm players
- Thống kê kill count, thời gian train
- Tự động quay lại bãi khi chết

### ✅ Popup xem màn hình
- Hiển thị grid các màn hình LDPlayer
- Screenshot real-time
- Điều khiển auto từng player riêng lẻ

### ✅ Templates tự động
- Tự động load tất cả templates từ thư mục `resources/images/`
- Hiển thị danh sách templates đã load
- Không cần upload thủ công

## 📦 Cài đặt

> **Xem hướng dẫn chi tiết trong [INSTALL.md](INSTALL.md)**

### Cài đặt nhanh:
```bash
# 1. Cài Python từ Microsoft Store hoặc python.org
# 2. Cài thư viện
pip install -r requirements.txt

# 3. Chạy demo (không cần LDPlayer)
python demo_ui.py

# 4. Chạy ứng dụng đầy đủ (cần LDPlayer)
python main.py
```

## 🎮 Cách sử dụng

### Tab "Quản lý LDPlayer"

1. **Làm mới danh sách**: Click "Làm mới danh sách" để scan LDPlayer
2. **Chọn players**: Tick checkbox của LDPlayer muốn chạy auto
3. **Chọn bãi train**: Chọn bãi từ dropdown
4. **Bắt đầu**: Click "Bắt đầu Train (Đã chọn)"

### Workflow ví dụ:
```
1. Có 5 LDPlayer: LD1, LD2, LD3, LD4, LD5
2. Tick chọn LD1, LD3, LD5  
3. Chọn "bai_1 - Bãi train cấp 1-20"
4. Click "Bắt đầu Train (Đã chọn)"
→ Chỉ LD1, LD3, LD5 sẽ chạy auto train
```

### Popup xem màn hình:
1. Click "Xem danh sách màn hình"
2. Xem screenshot real-time của tất cả LDPlayer
3. Click checkbox "Auto" để bật/tắt auto
4. Click "Bắt đầu tất cả" để chạy auto toàn bộ

## 🔧 Cấu trúc dự án

```
auto_tool/
├── core/
│   ├── adb_controller.py          # Điều khiển ADB multi-device
│   ├── auto_train.py              # Logic auto train
│   ├── image_recognition.py       # Nhận diện hình ảnh
│   ├── ldplayer_manager.py        # Quản lý LDPlayer
│   └── multi_player_auto.py       # Auto nhiều players
├── ui/
│   ├── main_window.py             # Giao diện chính
│   └── screen_viewer_dialog.py    # Popup xem màn hình
├── resources/
│   └── images/                    # Templates (tự động load)
├── main.py                        # File chính
├── demo_ui.py                     # Demo giao diện
├── requirements.txt               # Thư viện cần thiết
├── INSTALL.md                     # Hướng dẫn cài đặt
└── README.md                      # Hướng dẫn sử dụng
```

## 🖼️ Thêm Templates

1. Chụp ảnh các icon/button trong game
2. Lưu vào thư mục `resources/images/` với tên mô tả (ví dụ: `boss_icon.png`)
3. Restart ứng dụng → Templates sẽ tự động load
4. Kiểm tra trong tab "Templates" để xem danh sách đã load

## 🎯 Tính năng nổi bật

- **Multi-threading**: Mỗi LDPlayer chạy trên thread riêng
- **Real-time monitoring**: Cập nhật trạng thái liên tục
- **Batch operations**: Điều khiển nhiều players cùng lúc
- **Individual control**: Điều khiển từng player riêng lẻ
- **Auto-detection**: Tự động phát hiện LDPlayer mới
- **Color-coded status**: Trạng thái với màu sắc dễ nhìn

## 🐛 Troubleshooting

### Lỗi "Python was not found"
- Cài đặt Python từ Microsoft Store hoặc python.org
- Thêm Python vào PATH

### Không tìm thấy LDPlayer
- Đảm bảo LDPlayer đang chạy
- Bật ADB debugging trong LDPlayer
- Click "Làm mới danh sách"

### Không kết nối được ADB
- Restart LDPlayer
- Restart ADB: `adb kill-server && adb start-server`
- Kiểm tra port LDPlayer (thường 5555, 5557, 5559...)

## 📝 Ghi chú

- Ứng dụng tự động scan LDPlayer mỗi 10 giây
- Log hiển thị riêng cho từng player với tên
- Có thể chạy demo UI để test giao diện mà không cần LDPlayer thật
