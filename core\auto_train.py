import time
import random
from typing import Dict, Tuple, List, Optional
from datetime import datetime

class AutoTrain:
    def __init__(self, adb_controller, image_recognition):
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.running = False
        
        # C<PERSON>u hình bãi train
        self.train_configs = {
            'bai_1': {
                'name': 'Bãi train cấp 1-20',
                'npc_teleport': 'npc_xa_phu',
                'map_name': 'thanh_do',
                'train_pos': (850, 450),  # Vị trí click trên minimap
                'mob_types': ['quai_1', 'quai_2']
            },
            'bai_2': {
                'name': 'Bãi train cấp 20-40',
                'npc_teleport': 'npc_xa_phu',
                'map_name': 'phuong_tuong',
                'train_pos': (900, 400),
                'mob_types': ['quai_3', 'quai_4']
            }
        }
        
        self.current_train_spot = None
        self.death_count = 0
        self.start_time = None
        self.kill_count = 0
        
    def start_train(self, train_spot_key: str):
        """Bắt đầu auto train tại bãi đượ<PERSON> chọn"""
        if train_spot_key not in self.train_configs:
            print(f"Không tìm thấy bãi train: {train_spot_key}")
            return
        print(f"Bãi train: {self.train_configs[train_spot_key]}")
        self.current_train_spot = self.train_configs[train_spot_key]
        self.running = True
        self.start_time = datetime.now()
        self.death_count = 0
        self.kill_count = 0
        
        print(f"Bắt đầu train tại: {self.current_train_spot['name']}")
        
        # Loop chính
        while self.running:
            try:
                screenshot = self.adb.screenshot()
                if screenshot is None:
                    time.sleep(1)
                    continue
                
                # Kiểm tra xem có đang ở thành không
                if self._is_in_town(screenshot):
                    print("Phát hiện đang ở thành, chuẩn bị quay lại bãi train...")
                    self._return_to_train_spot()
                    continue
                
                # # Kiểm tra HP thấp
                # if self._check_low_hp(screenshot):
                #     self._use_hp_potion(screenshot)
                
                # # Kiểm tra MP thấp
                # if self._check_low_mp(screenshot):
                #     self._use_mp_potion(screenshot)
                
                # # Tìm và đánh quái
                # if not self._is_in_combat(screenshot):
                #     self._find_and_attack_mob(screenshot)
                # else:
                #     # Đang đánh, sử dụng skill
                #     self._use_combat_skills(screenshot)
                
                # # Nhặt đồ nếu có
                # self._pick_items(screenshot)
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"Lỗi trong quá trình train: {e}")
                time.sleep(1)
    
    def _is_in_town(self, screenshot) -> bool:
        """Kiểm tra xem có đang ở trong thành không"""
        # Tìm các dấu hiệu của thành
        town_indicators = [
            # 'town_npc_icon',      # Icon NPC trong thành
            # 'town_shop_icon',     # Icon cửa hàng
            # 'town_storage_icon',  # Icon kho
            # 'revive_point'        # Điểm hồi sinh
            'dai_ly_phu'
        ]
        
        for indicator in town_indicators:
            if self.image_rec.find_template(screenshot, indicator, threshold=0.7):
                return True
        
        # Kiểm tra tên map hiện tại
        map_region = self._get_map_name_region()

        if map_region:
            map_text = self.image_rec.get_text_from_region(
                screenshot, 
                map_region[0], map_region[1], 
                map_region[2], map_region[3]
            )
            print(f"Map: {map_text}")
            town_names = ['Thành', 'Tân Thủ Thôn', 'Phượng Tường', 'Đại Lý']
            for town in town_names:
                if town.lower() in map_text.lower():
                    return True
        
        return False
    
    def _return_to_train_spot(self):
        """Quay lại bãi train từ thành"""
        if not self.current_train_spot:
            return
        
        print("Bắt đầu di chuyển về bãi train...")
        
        # Bước 1: Tìm NPC xa phu
        screenshot = self.adb.screenshot()
        npc_pos = self._find_npc_teleport(screenshot)
        return
        if npc_pos:
            # Click vào NPC
            self.adb.tap(npc_pos[0], npc_pos[1])
            time.sleep(2)
            
            # Tìm và click vào map cần đến
            screenshot = self.adb.screenshot()
            map_button = self.image_rec.find_template(
                screenshot, 
                f"map_{self.current_train_spot['map_name']}"
            )
            
            if map_button:
                self.adb.tap(map_button[0], map_button[1])
                time.sleep(1)
                
                # Click xác nhận dịch chuyển
                confirm_pos = self.image_rec.find_template(screenshot, "confirm_teleport")
                if confirm_pos:
                    self.adb.tap(confirm_pos[0], confirm_pos[1])
                    time.sleep(5)  # Đợi dịch chuyển
        
        # Bước 2: Di chuyển đến vị trí train
        self._move_to_train_position()
    
    def _find_npc_teleport(self, screenshot) -> Optional[Tuple[int, int]]:
        """Tìm NPC xa phu để dịch chuyển"""
        # Mở bản đồ NPC
        # map_button = self.image_rec.find_template(screenshot, "map_button")
        # if map_button:
        #     # self.adb.tap(map_button[0], map_button[1])
        #     time.sleep(1)
        #     return
        #     # Click tab NPC
        #     screenshot = self.adb.screenshot()
        #     npc_tab = self.image_rec.find_template(screenshot, "npc_tab")
        #     if npc_tab:
        #         self.adb.tap(npc_tab[0], npc_tab[1])
        #         time.sleep(0.5)
        
        # Mở bản đồ NPC
        self.adb.tap(1750, 180)
        time.sleep(1)
        screenshot = self.adb.screenshot()
        # Nhập tên NPC
        self.adb.tap(260, 306)
        self.adb.input_text('xa phu')
        self.adb.tap(68, 178)
        time.sleep(1)
        self.adb.tap(232, 387)
        while True:
            screenshot = self.adb.screenshot()
            npc_pos = self.image_rec.find_template(
                screenshot, 
                "xa_phu"
            )
            if npc_pos:
                break
            time.sleep(3)
        self.adb.tap(npc_pos[0], npc_pos[1])
        time.sleep(0.5)
        screenshot = self.adb.screenshot()
        self.adb.tap(1301, 826)
        time.sleep(0.5)
        screenshot = self.adb.screenshot()
        self.adb.tap(670, 706)
        time.sleep(0.5)
        screenshot = self.adb.screenshot()
        self.adb.tap(670, 706)
        time.sleep(1)
        screenshot = self.adb.screenshot()
        self.adb.tap(1750, 180)
        time.sleep(0.5)
        screenshot = self.adb.screenshot()
        self.adb.tap(1756, 241)
        time.sleep(0.5)
        screenshot = self.adb.screenshot()
        self.adb.tap(1375, 353)
        return
        # Click tab NPC
        screenshot = self.adb.screenshot()
        npc_tab = self.image_rec.find_template(screenshot, "npc_tab")
        if npc_tab:
            self.adb.tap(npc_tab[0], npc_tab[1])
            time.sleep(0.5)

        # Tìm NPC xa phu
        screenshot = self.adb.screenshot()
        npc_pos = self.image_rec.find_template(
            screenshot, 
            self.current_train_spot['npc_teleport']
        )
        
        if npc_pos:
            # Click để auto đến NPC
            self.adb.tap(npc_pos[0], npc_pos[1])
            time.sleep(0.5)
            
            # Tìm nút tự động di chuyển
            auto_move = self.image_rec.find_template(screenshot, "auto_path_button")
            if auto_move:
                self.adb.tap(auto_move[0], auto_move[1])
                
                # Đợi di chuyển đến NPC
                self._wait_arrive_destination(30)
                
                # Đóng bản đồ
                close_button = self.image_rec.find_template(screenshot, "close_button")
                if close_button:
                    self.adb.tap(close_button[0], close_button[1])
                
                return npc_pos
        
        return None
    
    def _move_to_train_position(self):
        """Di chuyển đến vị trí train"""
        train_x, train_y = self.current_train_spot['train_pos']
        
        # Click vào minimap
        self.adb.tap(train_x, train_y)
        time.sleep(1)
        
        # Hoặc mở bản đồ lớn và click
        screenshot = self.adb.screenshot()
        map_button = self.image_rec.find_template(screenshot, "map_button")
        if map_button:
            self.adb.tap(map_button[0], map_button[1])
            time.sleep(1)
            
            # Click vào vị trí train trên bản đồ
            self.adb.tap(train_x, train_y)
            time.sleep(0.5)
            
            # Auto path
            auto_button = self.image_rec.find_template(screenshot, "auto_path_button")
            if auto_button:
                self.adb.tap(auto_button[0], auto_button[1])
            
            # Đóng bản đồ
            close_button = self.image_rec.find_template(screenshot, "close_button")
            if close_button:
                self.adb.tap(close_button[0], close_button[1])
        
        # Đợi đến nơi
        self._wait_arrive_destination(20)
    
    def _wait_arrive_destination(self, timeout: int = 30):
        """Đợi nhân vật di chuyển đến đích"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            screenshot = self.adb.screenshot()
            
            # Kiểm tra xem còn đang di chuyển không
            moving_indicator = self.image_rec.find_template(
                screenshot, 
                "auto_moving_indicator"
            )
            
            if not moving_indicator:
                # Đã đến nơi
                return True
            
            time.sleep(1)
        
        return False
    
    def _find_and_attack_mob(self, screenshot):
        """Tìm và tấn công quái"""
        # Tìm quái gần nhất
        for mob_type in self.current_train_spot['mob_types']:
            mobs = self.image_rec.find_all_templates(screenshot, mob_type, threshold=0.7)
            
            if mobs:
                # Chọn quái gần nhất (thường ở giữa màn hình)
                screen_center = (self.adb.get_screen_size()[0] // 2, 
                               self.adb.get_screen_size()[1] // 2)
                
                closest_mob = min(mobs, key=lambda m: 
                    ((m[0] - screen_center[0])**2 + (m[1] - screen_center[1])**2)**0.5
                )
                
                # Click vào quái
                self.adb.tap(closest_mob[0], closest_mob[1])
                time.sleep(0.5)
                
                # Tấn công
                attack_button = self.image_rec.find_template(screenshot, "attack_button")
                if attack_button:
                    self.adb.tap(attack_button[0], attack_button[1])
                    self.kill_count += 1
                    return True
        
        # Không tìm thấy quái, di chuyển ngẫu nhiên
        self._random_move_in_area()
        return False
    
    def _random_move_in_area(self):
        """Di chuyển ngẫu nhiên trong khu vực train"""
        # Random offset từ vị trí train chính
        offset_x = random.randint(-100, 100)
        offset_y = random.randint(-100, 100)
        
        train_x, train_y = self.current_train_spot['train_pos']
        new_x = train_x + offset_x
        new_y = train_y + offset_y
        
        # Click vào minimap
        self.adb.tap(new_x, new_y)
        time.sleep(2)
    
    def _is_in_combat(self, screenshot) -> bool:
        """Kiểm tra đang trong combat"""
        # Tìm thanh máu quái
        monster_hp = self.image_rec.find_template(screenshot, "monster_hp_bar")
        # Hoặc tìm hiệu ứng đánh
        combat_effect = self.image_rec.find_template(screenshot, "combat_effect")
        
        return monster_hp is not None or combat_effect is not None
    
    def _use_combat_skills(self, screenshot):
        """Sử dụng skill khi đang đánh"""
        skill_positions = [
            (100, 600),  # Skill 1
            (180, 600),  # Skill 2
            (260, 600),  # Skill 3
            (340, 600),  # Skill 4
        ]
        
        # Random sử dụng skill
        if random.random() < 0.3:  # 30% cơ hội dùng skill
            skill_idx = random.randint(0, len(skill_positions) - 1)
            self.adb.tap(skill_positions[skill_idx][0], skill_positions[skill_idx][1])
    
    def _check_low_hp(self, screenshot) -> bool:
        """Kiểm tra HP thấp"""
        # Cách 1: Dùng màu thanh HP
        hp_bar_region = (50, 50, 200, 20)  # Vùng thanh HP
        low_hp_color = (255, 0, 0)  # Màu đỏ
        
        # Cách 2: Dùng template matching cho icon cảnh báo HP thấp
        low_hp_warning = self.image_rec.find_template(screenshot, "low_hp_warning")
        
        return low_hp_warning is not None
    
    def _check_low_mp(self, screenshot) -> bool:
        """Kiểm tra MP thấp"""
        low_mp_warning = self.image_rec.find_template(screenshot, "low_mp_warning")
        return low_mp_warning is not None
    
    def _use_hp_potion(self, screenshot):
        """Sử dụng HP"""
        hp_slot = self.image_rec.find_template(screenshot, "hp_potion_slot")
        if hp_slot:
            self.adb.tap(hp_slot[0], hp_slot[1])
    
    def _use_mp_potion(self, screenshot):
        """Sử dụng MP"""
        mp_slot = self.image_rec.find_template(screenshot, "mp_potion_slot")
        if mp_slot:
            self.adb.tap(mp_slot[0], mp_slot[1])
    
    def _pick_items(self, screenshot):
        """Nhặt đồ rơi"""
        items = self.image_rec.find_all_templates(screenshot, "item_drop", threshold=0.6)
        
        for item in items[:3]:  # Nhặt tối đa 3 item
            self.adb.tap(item[0], item[1])
            time.sleep(0.3)
    
    def _get_map_name_region(self) -> Optional[Tuple[int, int, int, int]]:
        """Lấy vùng hiển thị tên map"""
        # Thường ở góc trên bên trái
        return (10, 80, 200, 30)
    
    def get_train_stats(self) -> Dict:
        """Lấy thống kê train"""
        if not self.start_time:
            return {}
        
        duration = datetime.now() - self.start_time
        hours = duration.total_seconds() / 3600
        
        return {
            'duration': str(duration).split('.')[0],
            'kill_count': self.kill_count,
            'death_count': self.death_count,
            'kills_per_hour': int(self.kill_count / hours) if hours > 0 else 0,
            'current_spot': self.current_train_spot['name'] if self.current_train_spot else 'N/A'
        }
    
    def stop(self):
        """Dừng auto train"""
        self.running = False
        print(f"Đã dừng train. Thống kê: {self.get_train_stats()}")